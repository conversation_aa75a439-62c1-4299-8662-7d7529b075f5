import queryString from 'query-string';
import { baseApiService } from './BaseApiService';

export interface Enterprise {
  id: string;
  epName: string;
  enterpriseLogo?: string;
  location?: string;
  createdAt: string;
  updatedAt: string;
}

export interface EnterprisesListResponse {
  data: Enterprise[];
  total: number;
  page: number;
  limit: number;
}

export interface FetchEnterprisesParams {
  page?: number;
  limit?: number;
  search?: string;
}

class EnterpriseService {
  // Get Enterprises List
  async getEnterprisesList(params: FetchEnterprisesParams = {}) {
   
    const queryParams = queryString.stringify(params);
    const url = `/enterprise/get-enterprise-details?${queryParams}`;
    
    return baseApiService.get<EnterprisesListResponse>(url);
  }

  // Get Enterprise by ID
  async getEnterpriseById(id: number) {
    return baseApiService.get<{ data: Enterprise }>(`/enterprise/get-enterprise-details/${id}`);
  }
}

export const enterpriseService = new EnterpriseService();
