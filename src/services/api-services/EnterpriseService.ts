import { baseApiService } from './BaseApiService';

export interface Enterprise {
  id: string;
  epName: string;
  enterpriseLogo?: string;
  location?: string;
  createdAt: string;
  updatedAt: string;
}

export interface EnterprisesListResponse {
  data: Enterprise[];
  total: number;
  page: number;
  limit: number;
}

export interface FetchEnterprisesParams {
  page?: number;
  limit?: number;
  search?: string;
}

class EnterpriseService {
  // Get Enterprises List
  async getEnterprisesList(params: FetchEnterprisesParams = {}) {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/get-enterprise-details?${queryString}` : '/get-enterprise-details';
    
    return baseApiService.get<EnterprisesListResponse>(url);
  }

  // Get Enterprise by ID
  async getEnterpriseById(id: string) {
    return baseApiService.get<{ data: Enterprise }>(`/get-enterprise-details/${id}`);
  }
}

export const enterpriseService = new EnterpriseService();
