import { EnterpriseActionType } from './actions.constants';

// Interfaces
export interface CreateEnterprisePayload {
  ep_name: string;
  ep_logo?: File;
  location?: string;
  ep_id: number;
}


export interface FetchEnterprisesPayload {
  page?: number;
  limit?: number;
  search?: string;
}


// Fetch Enterprise List
export const fetchEnterprisesList = (payload: {
  filters: FetchEnterprisesPayload;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseActionType.GET_ENTERPRISES,
  payload,
});

// Fetch Enterprise  by ID
export const fetchEnterpriseById = (payload: {
  id: string;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}) => ({
  type: EnterpriseActionType.GET_ENTERPRISE,
  payload,
});
