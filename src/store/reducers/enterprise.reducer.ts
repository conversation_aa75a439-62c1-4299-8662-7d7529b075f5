import {
  createEntityAdapter,
  createSlice,
  EntityState,
  PayloadAction,
} from '@reduxjs/toolkit';
import { EnterpriseAdmin } from 'models/entities/EnterpriseAdmin';
import { BaseEntityStore } from 'store/types/BaseEntityStoreType';
import { PaginationMeta } from '../../types/pagination.types';

const enterpriseAdapter = createEntityAdapter<EnterpriseAdmin>();

const initialState: EntityState<EnterpriseAdmin> &
  BaseEntityStore &
  PaginationMeta = {
  ...enterpriseAdapter.getInitialState(),
  create: {},
  update: {},
  get: {},
  list: {},
  paginationMeta: {},
};

export const enterpriseSlice = createSlice({
  name: 'enterprise',
  initialState,
  reducers: {
    fetch_enterprise_list: enterpriseAdapter.addMany,
    add_one: enterpriseAdapter.setOne,
    set_get_enterprise_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.get = { ...state.get, loading };
    },
    set_get_enterprise_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.get = { ...state.get, loading: false, error, message };
    },
    set_enterprises_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.list = { ...state.list, loading };
    },
    set_enterprises_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.list = { ...state.list, error, message };
    },

    set_page_data: (state, action: PayloadAction<PaginationMeta>) => {
      state.paginationMeta = action.payload.paginationMeta;
    },

  },
});

export const {
  fetch_enterprise_list: getEnterprises,
  set_enterprises_loading: setEnterpriseLoading,
  set_enterprises_error: setEnterpriseError,
  set_page_data: setEnterprisesPageData,
} = enterpriseSlice.actions;

export default enterpriseSlice.reducer;
